package com.iqiyi.vip.domain.sku.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/8/31 14:20
 */
@Data
public class SkuSpecAttributes {

    private String promotionCode;
    private String productCode;
    private String ruleCode;
    private Integer amount;
    private Integer autoRenew;

    private Integer originalPrice;

    private Integer relativePrice;

    private Integer incomeBelongType;

    private String subIncomeBelongType;

    private String purchaseStyle;

    /**
     * 库存总数量
     */
    private Integer totalInventory;

    private String giftCode;

    private String customerCode;

    private String contractCode;

    /**
     * 商品联系人
     */
    private String email;

    /**
     * 单用户参与次数限制
     */
    private Integer constraintLimitCountUser;

    /**
     * 权益发放类型（0：直接充值  1：手动领取）
     */
    private Integer constraintSendType;

    private Integer receiveDeadlineRelativeDays;

    private Long receiveDeadlineAbsolute;

    /**
     * 是否支持切换账号领取(0:不支持，1:支持)constraintSendType=1或2时必填
     */
    private Integer changeAccount;

    /**
     * 切换账号类型(0:手机号，1:QQ，2:QQ音乐)changeAccount=1时必填
     */
    private Integer accountType;

    /**
     * 权益发放类型(0：权益直充 1:激活码发放)
     */
    private Integer giftType;

    /**
     * 兑换码批次
     */
    private String partnerExpCardBatch;

    /**
     * 退单是否回收商品权益 0:不回收  1:回收
     */
    private Integer refundPartnerType;

    /**
     * 处理退单最晚截止时间
     */
    private Long refundPartnerDeadline;

    private Integer settleCalculateType;

    private Integer settlePercent;

    private Integer settlePrice;

    /**
     * 是否有福利履约(0:否，1:是)
     */
    private String benefitSkuId;

    /**
     * 代金券批次
     */
    private String batchNo;

    /**
     * 是否设置库存：1：是；0：否；
     */
    private Integer inventoryValidFlag;

    /**
     * 是否回收库存   1-是 0-否
     */
    private Integer inventoryRecoveryFlag;

    /**
     * 爱奇艺商城代金券
     */
    private String virtualCouponBatch;

    /**
     * 云包场-加座商品关联的skuId
     */
    private String relatedSkuId;

    /**
     * 值=1 代表 福利系统创建的
     */
    private String relOrCreate;

    /**
     * 是否预付费
     */
    private Integer prePaid;

    /**
     * 商品标识, 1:常规连包  2：芝麻购  3：首X期优惠  4:X元N天卡  5:纯签约 6:兑换升级
     */
    private Integer skuIdentifier;

    /**
     * 限购规则code
     */
    private String buyLimitRuleCode;

    /**
     * 会员类型  56基础  1黄金 3白金 4星钻
     */
    private String vipType;

    /**
     * 会员时长
     */
    private String timeLength;

    private String periodUnit;

    /**
     * 展示名称
     */
    private String displayName;
}
