package com.iqiyi.vip.acl.serviceimpl;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.iqiyi.vip.domain.coupon.service.CouponService;
import com.iqiyi.vip.domain.gift.entity.Gift;
import com.iqiyi.vip.domain.gift.repository.GiftRepository;
import com.iqiyi.vip.domain.gift.service.GiftService;
import com.iqiyi.vip.domain.goods.entity.Goods;
import com.iqiyi.vip.domain.goods.repository.GoodsRepository;
import com.iqiyi.vip.domain.inventory.service.InventoryService;
import com.iqiyi.vip.domain.monitors.service.MonitorService;
import com.iqiyi.vip.domain.order.entity.CheckOrderCanTerminateDetail;
import com.iqiyi.vip.domain.order.repository.OrderRepository;
import com.iqiyi.vip.domain.order.service.OrderService;
import com.iqiyi.vip.domain.refund.RefundRepository;
import com.iqiyi.vip.domain.refund.entity.RetreatResult;
import com.iqiyi.vip.domain.rights.entity.FulfillOrderAggregate;
import com.iqiyi.vip.domain.rights.entity.OpenOrderRights;
import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.domain.rights.entity.TerminateOrderAggregate;
import com.iqiyi.vip.domain.rights.factory.*;
import com.iqiyi.vip.domain.rights.repository.ReceiveRecordRepository;
import com.iqiyi.vip.domain.rights.repository.RightsRecordRepository;
import com.iqiyi.vip.domain.rights.repository.RightsRepository;
import com.iqiyi.vip.domain.rights.service.RightsService;
import com.iqiyi.vip.domain.rms.service.RmsService;
import com.iqiyi.vip.domain.settlement.service.SettlementService;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.entity.SkuBeforeBuyCheckAggregate;
import com.iqiyi.vip.domain.sku.repository.SkuRepository;
import com.iqiyi.vip.domain.sku.service.SkuService;
import com.iqiyi.vip.domain.spufulfillment.repository.SpuFulfillmentRepository;
import com.iqiyi.vip.domain.task.*;
import com.iqiyi.vip.domain.urlconfig.entity.UrlConfig;
import com.iqiyi.vip.domain.urlconfig.repository.UrlConfigRepository;
import com.iqiyi.vip.domain.vmc.repository.VmcRepository;
import com.iqiyi.vip.dto.base.BaseListResponse;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.order.CheckOrderCanTerminateDetailRes;
import com.iqiyi.vip.dto.rights.*;
import com.iqiyi.vip.dto.sku.SkuBatchQuery;
import com.iqiyi.vip.dto.sku.SkuBeforeBuyCheckDetailRes;
import com.iqiyi.vip.dto.sku.SkuPromotionRes;
import com.iqiyi.vip.dto.viptag.VipTagSaveReq;
import com.iqiyi.vip.enums.*;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.repository.ClusterAsyncTaskManager;
import com.iqiyi.vip.utils.AssertUtils;
import com.iqiyi.vip.utils.DateUtils;
import com.iqiyi.vip.utils.JacksonUtil;
import com.qiyi.vip.trade.dataservice.client.dto.OrderDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/9/11 11:25
 */
@Slf4j
@Service("rightsService")
public class RightsServiceImpl implements RightsService {

    @Resource
    private RightsRepository rightsRepository;
    @Resource
    private SettlementService settlementService;
    @Resource
    private ReceiveRecordRepository receiveRecordRepository;
    @Resource
    private RightsRecordRepository rightsRecordRepository;
    @Resource
    private ClusterAsyncTaskManager clusterAsyncTaskManager;
    @Resource
    private SkuRepository skuRepository;
    @Resource
    private SkuService skuService;
    @Resource
    private OrderService orderService;
    @Resource
    private CouponService couponService;
    @Resource
    private GiftRepository giftRepository;
    @Resource
    private GoodsRepository goodsRepository;
    @Resource
    private UrlConfigRepository urlConfigRepository;
    @Resource
    private RmsService rmsService;
    @Resource
    private MonitorService monitorService;
    @Resource
    private SpuFulfillmentRepository spuFulfillmentRepository;
    @Resource
    private GiftService giftService;
    @Resource
    private InventoryService inventoryService;
    @Resource
    private VmcRepository vmcRepository;
    @Resource
    private RefundRepository refundRepository;
    @Resource
    private OrderRepository orderRepository;

    @Value("${remindDrawSms.delaySeconds:8}")
    private Integer remindDrawSmsDelaySeconds;
    @Value("${vip.order.max.retry.count:3}")
    private Integer maxRetryCount;

    @Override
    public void doExtAfterInsertRightsRecord(ReceiveRightRecord receiveRightRecord) {
        //更新标签
        clusterAsyncTaskManager.insertTask(new VipTagTask(VipTagSaveReq.builder().uid(receiveRightRecord.getUid()).receiveFlag(false).build()));
    }

    @Override
    public BaseResponse<PartnerRightsResponse> openRights(OpenOrderRights openRights) {
        BaseResponse<PartnerRightsResponse> httpResDTO;
        Boolean needCallBackOrder = true;
        if (VipGiftCodeMappingEnum.containsGiftCode(openRights.getGift().getCode())) {
            if (TradeOrderStatusEnum.PRE_PAID.getStatus() == openRights.getOrderInfo().getStatus()) {
                //1.会员预付费订单，需要调用交易的asOpened接口开通权益，该接口可以将status由10变成1，不需要回调交易
                httpResDTO = rightsRepository.openRights(RightsFactory.buildOpenPartnerRightsReq(openRights));
                needCallBackOrder = false;
            } else {
                httpResDTO = handVipPaidOrderRights(openRights);
            }
        } else {
            httpResDTO = rightsRepository.openRights(RightsFactory.buildOpenPartnerRightsReq(openRights));
        }
        openRights.setNeedCallBackOrder(needCallBackOrder);
        if (null != httpResDTO && null != httpResDTO.getData() && null != httpResDTO.getData().getSendStatus() && !httpResDTO.getData().needCancel()) {
            doAfterOpenRights(httpResDTO.getData(), openRights);
        } else {
            log.error("[partnerRightsResponse:{}][openRightsReq:{}]", httpResDTO, openRights);
        }
        return httpResDTO;
    }

    private BaseResponse<PartnerRightsResponse> handVipPaidOrderRights(OpenOrderRights openRights) {
        BaseResponse<PartnerRightsResponse> httpResDTO;
        //2.会员非预付订单
        // 2.1 处理兑换升级的订单（回收低等级订单权益、取消并发操作的订单等）
        if (openRights.getSku().isExchangeUpgrade()) {
            handleExchangeUpgradeOrder(openRights);
        }
        //2.2 调用VMC接口开通权益
        httpResDTO = vmcRepository.openRights(RightsFactory.buildOpenVmcRightsReq(openRights.getOrderInfo()));
        if (null != httpResDTO && null != httpResDTO.getData() && httpResDTO.getData().needCancel()) {
            log.info("Vmc resp repeat buy, need cancel order. orderCode:{}", openRights.getOrderInfo().getOrderCode());
            orderRepository.cancel(openRights.getOrderInfo().getOrderCode());
        }
        return httpResDTO;
    }

    private void handleExchangeUpgradeOrder(OpenOrderRights openRights) {
        String orderCode = openRights.getOrderInfo().getOrderCode();
        RetreatResult result = refundRepository.retreatLowerRight(orderCode);

        if (result.isSuccess()) {
            log.info("Retry lower success, result:{}, req:{}", result, openRights);
            return;
        }
        int currentRetryCount = openRights.getOrderInfo().getCurrentRetryCount() == null ? 0 : openRights.getOrderInfo().getCurrentRetryCount();
        if (result.needCancel() && currentRetryCount > maxRetryCount) {
            log.info("Retry lower failed, need cancel order, result:{}, req:{}, maxRetryCount:{}", result, openRights, maxRetryCount);
            boolean cancelled = orderRepository.cancel(orderCode);
            if (cancelled) {
                log.info("Cancel order success, result:{}, req:{}, maxRetryCount:{}", result, openRights, maxRetryCount);
                throw new BizRuntimeException(CodeEnum.SUC_CANCEL_ORDER);
            } else {
                log.error("Cancel order failed, result:{}, req:{}, maxRetryCount:{}", result, openRights, maxRetryCount);
                throw new BizRuntimeException(CodeEnum.ERROR_RETREAT_LOWER_RIGHT);
            }
        }

        log.info("Retry lower failed, need retry order, result:{}, req:{}", result, openRights);
        throw new BizRuntimeException(CodeEnum.ERROR_RETREAT_LOWER_RIGHT);
    }

    private void doAfterOpenRights(PartnerRightsResponse partnerRightsResponse, OpenOrderRights openRights) {
        ReceiveRightRecord receiveRightRecord = receiveRecordRepository.queryByOrderCode(ReceiveRightRecordFactory.buildQuery(openRights));
        if (SendStatusEnum.isSyncSuccess(partnerRightsResponse.getSendStatus())) {
            //更新领取记录
            receiveRightRecord.setSendTime(DateUtils.long2DateNUllDefaultNow(partnerRightsResponse.getSendTime()));
            //BI使用receiveTime做统计，receiveTime为空代表待领取，要求必须领取成功receiveTime才可以有值。
            receiveRightRecord.setReceiveTime(new Date());
            receiveRightRecord.setReceiveStatus(ReceiveStatusEnum.RECEIVED.getStatus());
            receiveRightRecord.setSendType(ReceiveRightRecordSendType.INTERFACE.getType());
            //保存券码信息
            if (StringUtils.isNotBlank(partnerRightsResponse.getCouponCode())) {
                receiveRightRecord.setCouponCode(partnerRightsResponse.getCouponCode());
            }
            //设置结算信息
            settlementService.saveSettlementInfo(receiveRightRecord, openRights.getSettlement());
        }

        receiveRightRecord.setEncryptAccount(partnerRightsResponse.getAccount());
        receiveRightRecord.setEncryptMobile(partnerRightsResponse.getMobile());
        receiveRightRecord.setSendStatus(partnerRightsResponse.getSendStatus());
        receiveRightRecord.setSendRes(partnerRightsResponse.getSendRes());
        receiveRightRecord.setRemark(JacksonUtil.writeValueAsStringThrowException(partnerRightsResponse.getRightRecordRemark()));
        receiveRightRecord.setAmount(partnerRightsResponse.getAmount());
        receiveRecordRepository.updateByOrderCodeSelective(receiveRightRecord);

        if (SendStatusEnum.isSyncSuccess(partnerRightsResponse.getSendStatus())) {
            //回调交易系统，告知该订单权益已开通完成 一定要在上面更新完成后再插入task，如果task先插入，task又先于updateByOrderCodeSelective执行，就会出现被updateByOrderCodeSelective的值被覆盖的情况
            if (null == openRights.getNeedCallBackOrder() || openRights.getNeedCallBackOrder()) {
                //订单需要保证秒开，首次需同步调用，首次若用异步task调用，数据量大时task可能会延迟执行无法保证快速回调。
                orderService.paidOrderCallBackFailRetry(ReceiveRightRecordFactory.buildQuery(openRights));
            }

            if (openRights.getSku().needNotifyTag()) {
                //更新标签
                clusterAsyncTaskManager.insertTask(new VipTagTask(VipTagSaveReq.builder()
                    .uid(receiveRightRecord.getUid())
                    .receiveFlag(true)
                    .build()));
            }
            //给用户发领取成功精准触达消息
            clusterAsyncTaskManager.insertTask(new RightsExactMsgTask(ReceiveRightRecordFactory.buildRightsExactDrawSucTaskMsg(receiveRightRecord)));
        }

        // 发送权益开通消息
        clusterAsyncTaskManager.insertTask(new FulfillmentGrantMsgTask(receiveRightRecord, openRights, partnerRightsResponse));

        if (!SendStatusEnum.isSyncSuccess(partnerRightsResponse.getSendStatus())
            && monitorService.needAlarm(partnerRightsResponse.getSendRes(), openRights.getGift().getCode())) {
            log.error("[send fail][openRights:{},openPartnerRightsRes:{}]", openRights, partnerRightsResponse);
        }
    }

    @Override
    public BaseResponse<PartnerRightsResponse> terminateRights(TerminateOrderAggregate terminateOrderAggregate, ReceiveRightRecord paidRightRecord) {
        BaseResponse<PartnerRightsResponse> httpResDTO = rightsRepository
            .terminateRights(RightsFactory.buildRefundPartnerRightsReq(terminateOrderAggregate, paidRightRecord));
        if (null != httpResDTO && null != httpResDTO.getData() && null != httpResDTO.getData().getSendStatus()) {
            doAfterTerminateRights(terminateOrderAggregate, paidRightRecord, httpResDTO.getData());
        } else {
            log.error("[partnerRightsResponse null][terminateOrderAggregate:{}]", terminateOrderAggregate);
            paidRightRecord.setRefundStatus(ReceiveRightRecordRefundStatus.REFUND_FAIL.getStatus());
            receiveRecordRepository.updateByOrderCodeSelective(paidRightRecord);
        }
        return httpResDTO;
    }

    private void doAfterTerminateRights(TerminateOrderAggregate terminateOrderAggregate, ReceiveRightRecord paidRightRecord, PartnerRightsResponse partnerRightsResponse) {
        paidRightRecord.setRefundRes(partnerRightsResponse.getSendRes());
        if (SendStatusEnum.isSyncSuccess(partnerRightsResponse.getSendStatus())) {
            //权益回收成功
            paidRightRecord.setRefundStatus(ReceiveRightRecordRefundStatus.REFUND_SUC.getStatus());
            paidRightRecord.setRefundAmount(partnerRightsResponse.getAmount());
            receiveRecordRepository.updateByOrderCodeSelective(paidRightRecord);

            //通知交易退单权益已回收
            clusterAsyncTaskManager.insertTask(new RefundOrderCallBackTask(ReceiveRightRecordFactory.buildQuery(terminateOrderAggregate)));

            //回滚库存
            inventoryService.revertStock(terminateOrderAggregate);
        } else if (SendStatusEnum.UN_SEND.getStatus() == partnerRightsResponse.getSendStatus()) {
            //未调用，即未对接合作方接口的情况
            paidRightRecord.setRefundStatus(ReceiveRightRecordRefundStatus.REFUND_OFFLINE_SUC.getStatus());
            receiveRecordRepository.updateByOrderCodeSelective(paidRightRecord);
        } else {
            paidRightRecord.setRefundStatus(ReceiveRightRecordRefundStatus.REFUND_FAIL.getStatus());
            receiveRecordRepository.updateByOrderCodeSelective(paidRightRecord);
        }

        // 发送权益取消消息
        clusterAsyncTaskManager
            .insertTask(new FulfillmentRecycleMsgTask(paidRightRecord, terminateOrderAggregate.getRefundOrderInfo(), partnerRightsResponse));
    }

    @Override
    public BaseResponse<PartnerRightsResponse> manualOpenRights(FulfillOrderAggregate fulfillOrderAggregate) {
        //发送精准触达消息
        clusterAsyncTaskManager.insertTask(new RightsExactMsgTask(ReceiveRightRecordFactory.buildRightsExactTaskMsg(fulfillOrderAggregate)), DateUtils
            .getDateOfSeconds(new Date(), remindDrawSmsDelaySeconds));

        PartnerRightsResponse partnerRightsResponse = new PartnerRightsResponse();
        partnerRightsResponse.setSendStatus(SendStatusEnum.UN_SEND.getStatus());
        return BaseResponse.createSuccessResponse(partnerRightsResponse);
    }

    @Override
    public BaseResponse openRightsFailThenManual(FulfillOrderAggregate fulfillOrderAggregate) {
        BaseResponse<PartnerRightsResponse> rightsRes = openRights(RightsFactory.buildOpenPartnerRightsReq(fulfillOrderAggregate));
        if (null == rightsRes || null == rightsRes.getData() || null == rightsRes.getData().getSendStatus()
            || !SendStatusEnum.isSyncSuccess(rightsRes.getData().getSendStatus())) {
            //转为手动领取型
            ReceiveRightRecord dbReceiveRightRecord = receiveRecordRepository
                .queryByOrderCode(ReceiveRightRecordFactory.buildQuery(fulfillOrderAggregate));
            if (null == dbReceiveRightRecord) {
                log.error("[rightRecord null][orderCode:{},openRights:{}]", fulfillOrderAggregate.getOrderInfo()
                    .getOrderCode(), fulfillOrderAggregate);
                throw new BizRuntimeException(CodeEnum.ERROR_SYSTEM);
            }
            dbReceiveRightRecord.setReceiveType(SendTypeEnum.MANUAL_RECEIVE.getType());
            dbReceiveRightRecord.setReceiveDeadlineTime(fulfillOrderAggregate.getConstraint()
                .getReceiveDeadline(dbReceiveRightRecord.getOrderTime()));
            receiveRecordRepository.updateByOrderCodeSelective(dbReceiveRightRecord);
            return manualOpenRights(fulfillOrderAggregate);
        }
        return rightsRes;
    }

    @Override
    public CheckSkuCanReceiveResVO checkSkuCanReceiveBatch(CheckSkuCanReceiveReq req) {
        AssertUtils.notNull(req.getUid(), CodeEnum.ERROR_NOT_LOG);
        AssertUtils.notBlank(req.getSkuIds(), CodeEnum.ERROR_PARAM);

        //1.查询商品详情
        Map<String, Sku> skuId2SkuMap = skuRepository.batchQueryFromCache(SkuBatchQuery.builder().skuIds(req.getSkuIds()).onlyQueryValid(false).build());
        AssertUtils.mapNotEmpty(skuId2SkuMap, CodeEnum.ERROR_SKU_NULL);

        //2.计算需要验证的商品
        Map<String, Sku> needCheckSkuMap = Maps.newHashMap();
        for (Map.Entry<String, Sku> reqSkuMap : skuId2SkuMap.entrySet()) {
            Sku sku = reqSkuMap.getValue();
            //打包购需要查是否有子商品权益
            if (null != sku && SpuEnum.isPackageSpu(sku.getSpuId()) && CollectionUtils.isNotEmpty(sku.getSubSkuList())) {
                for (Sku subSku : sku.getSubSkuList()) {
                    needCheckSkuMap.put(subSku.getSkuId(), subSku);
                }
            } else {
                needCheckSkuMap.put(sku.getSkuId(), sku);
            }
        }

        //3.重置changeAccount
        skuService.resetChangeAccount(req.getUid(), needCheckSkuMap);

        //4.验证用户是否有这些商品的可领权益
        Map<String, CheckSubSkuCanReceiveResVO> skuId2CheckResMap = Maps.newHashMap();
        for (Map.Entry<String, Sku> needCheckSku : needCheckSkuMap.entrySet()) {
            CheckSubSkuCanReceiveResVO checkSkuCanReceive = CheckRightsFactory
                .buildCheckSubSkuCanReceiveResVO(req.getUid(), needCheckSku.getValue(), receiveRecordRepository);
            skuId2CheckResMap.put(needCheckSku.getKey(), checkSkuCanReceive);
        }

        //5.组织返回结果
        List<CheckSubSkuCanReceiveResVO> rightsList = Lists.newArrayList();
        List<String> reqSkuIds = Splitter.on(",").omitEmptyStrings().splitToList(req.getSkuIds());
        for (String reqSkuId : reqSkuIds) {
            rightsList.add(CheckRightsFactory.buildCheckSubSkuCanReceiveResVOResetPackSku(reqSkuId, skuId2SkuMap.get(reqSkuId), skuId2CheckResMap));
        }
        CheckSkuCanReceiveResVO res = new CheckSkuCanReceiveResVO();
        res.setRightsList(rightsList);
        return res;
    }

    @Override
    public BaseResponse receiveBySkuId(ReceiveBySkuIdReq req) {
        ReceiveRightsReq receiveRightsReq = ReceiveRightsReq.builder()
            .uid(req.getUid())
            .skuId(req.getSkuId())
            .receiveAccount(req.getReceiveAccount())
            .build();
        return receiveRights(receiveRightsReq);
    }

    @Override
    public BaseResponse receiveByOrderCode(ReceiveByOrderCodeReq req) {
        AssertUtils.notBlank(req.getOrderCode(), CodeEnum.ERROR_PARAM);

        // 通过订单号从数据库查询领取记录获取skuId
        ReceiveRightRecordQryCon qryCon = ReceiveRightRecordQryCon.builder()
            .uid(req.getUid())
            .orderCode(req.getOrderCode())
            .build();
        List<ReceiveRightRecord> recordList = receiveRecordRepository.queryByConFromMySql(qryCon);
        AssertUtils.notEmpty(recordList, CodeEnum.ERROR_ORDER_NON);

        ReceiveRightRecord record = recordList.get(0);
        ReceiveRightsReq receiveRightsReq = ReceiveRightsReq.builder()
            .uid(req.getUid())
            .skuId(record.getSkuId())
            .orderCode(req.getOrderCode())
            .receiveAccount(req.getReceiveAccount()).build();

        return receiveRights(receiveRightsReq);
    }

    @Override
    public BaseListResponse userRightsList(UserRightsListQry qry) {
        return new BaseListResponse(CodeEnum.SUCCESS, listFilterRMSSkuId(qry));
    }

    @Override
    public List<KeFuUserRightsListRes> keFuUserRightsList(KeFuUserRightsListQry qry) {
        AssertUtils.notNull(qry.getUid(), CodeEnum.ERROR_PARAM);
        AssertUtils.notBlank(qry.getOrderCodeList(), CodeEnum.ERROR_PARAM);
        List<String> orderCodeReqList = Splitter.on(",").omitEmptyStrings().splitToList(qry.getOrderCodeList());
        //查询用户权益记录
        List<ReceiveRightRecord> userRightsList = receiveRecordRepository.queryByCon(ReceiveRightRecordQryCon.builder()
            .uid(Long.valueOf(qry.getUid()))
            .orderCodeList(orderCodeReqList)
            .build());

        //组织返回数据
        List<KeFuUserRightsListRes> resList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(userRightsList)) {
            for (String orderCode : orderCodeReqList) {
                resList.add(KeFuUserRightsListResFactory.buildNoRights(qry.getUid(), orderCode));
            }
            return resList;
        }
        Map<String, ReceiveRightRecord> orderCodeToRecordMap = userRightsList.stream()
            .collect(Collectors.toMap(ReceiveRightRecord::getOrderCode, Function.identity(), (key1, key2) -> key2));

        //查商品信息
        Set<String> skuIdSet = userRightsList.stream().map(ReceiveRightRecord::getSkuId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Map<String, Sku> skuId2SkuMap = CollectionUtils.isEmpty(skuIdSet) ? Maps.newHashMap()
            : skuRepository.batchQueryFromCache(SkuBatchQuery.builder().skuIds(String.join(",", skuIdSet)).onlyQueryValid(false).build());

        for (String orderCode : orderCodeReqList) {
            ReceiveRightRecord receiveRightRecord = orderCodeToRecordMap.get(orderCode);
            KeFuUserRightsListRes res;
            if (null == receiveRightRecord) {
                res = KeFuUserRightsListResFactory.buildNoRights(qry.getUid(), orderCode);
            } else {
                res = KeFuUserRightsListResFactory.buildHasRights(receiveRightRecord, skuId2SkuMap);
            }
            resList.add(res);
        }
        return resList;
    }

    private List<UserRightsListRes> listFilterRMSSkuId(UserRightsListQry qry) {
        if (!NumberUtils.isDigits(qry.getUid())) {
            throw new BizRuntimeException(CodeEnum.ERROR_PARAM);
        }
        List<UserRightsListRes> userRightsList = listReceiveChanceList(qry);
        return CollectionUtils.isEmpty(userRightsList) ? Collections.EMPTY_LIST : filterRMSSkuId(userRightsList);
    }

    @Override
    public BaseListResponse userRightsCount(UserRightsListQry qry) {
        List<UserRightsListRes> userRightsList = listFilterRMSSkuId(qry);
        Map<ReceiveChanceStatusEnum, List<UserRightsListRes>> countResultMap = userRightsList.parallelStream()
            .collect(Collectors.groupingBy(UserRightsListRes::getStatus));
        List<UserRightsCountRes> userRightsCountResList = Lists.newArrayList();
        countResultMap.forEach((receiveChanceStatus, list) -> {
            UserRightsCountRes recordItem = new UserRightsCountRes();
            recordItem.setStatus(receiveChanceStatus);
            recordItem.setCount(list.size());
            userRightsCountResList.add(recordItem);
        });
        return new BaseListResponse(CodeEnum.SUCCESS, userRightsCountResList);
    }

    public List<UserRightsListRes> listReceiveChanceList(UserRightsListQry qry) {
        Set<ReceiveChanceStatusEnum> statusListQry = convert2ReceiveChanceStatusEnumSet(qry.getStatus());
        List<ReceiveRightRecord> userRightsList = receiveRecordRepository.queryByConFromMySql(ReceiveRightRecordQryCon.builder()
            .uid(Long.valueOf(qry.getUid()))
            .build());
        userRightsList.parallelStream().forEach(record -> {
            if (record.getReceiveStatus() == null) {
                record.setReceiveStatus(ReceiveStatusEnum.PENDING_RECEIVE.getStatus());
            }
            if (record.getOrderTime() == null) {
                record.setOrderTime(record.getCreateTime());
            }
        });
        return queryUserRightsListResByCon(userRightsList, qry.getPresentNo(), statusListQry, ReceiveChanceOrderEnum.of(qry.getOrder()));
    }

    @Override
    public List<UserRightsCountByPackSkuIdRes> userRightsCountByPackSkuId(UserRightsCountQryByPackSkuId qry) {
        AssertUtils.notNull(qry.getUid(), CodeEnum.ERROR_NOT_LOG);
        Map<String, List<String>> packSkuId2PromotionCodeListMap = skuService.queryPackSkuId2PromotionCodeFromCache(qry.getPackSkuIds());
        if (MapUtils.isEmpty(packSkuId2PromotionCodeListMap)) {
            return Collections.EMPTY_LIST;
        }
        Set<String> promotionCodeSet = Sets.newHashSet();
        for (Map.Entry<String, List<String>> entry : packSkuId2PromotionCodeListMap.entrySet()) {
            promotionCodeSet.addAll(entry.getValue());
        }
        if (CollectionUtils.isEmpty(promotionCodeSet)) {
            return Collections.EMPTY_LIST;
        }

        //查询用户记录
        UserRightsListQry userRightsListQry = new UserRightsListQry();
        userRightsListQry.setUid(String.valueOf(qry.getUid()));
        List<Integer> statusList = Lists.newArrayList();
        statusList.add(ReceiveChanceStatusEnum.PENDING_RECEIVE.getStatus());
        statusList.add(ReceiveChanceStatusEnum.RECEIVED.getStatus());
        userRightsListQry.setStatus(Joiner.on(",").join(statusList));
        List<UserRightsListRes> userRightsListResList = listReceiveChanceList(userRightsListQry);

        if (CollectionUtils.isEmpty(userRightsListResList)) {
            return Collections.EMPTY_LIST;
        }
        userRightsListResList = userRightsListResList.stream()
            .filter(userReceiveChanceResDTO -> promotionCodeSet.contains(userReceiveChanceResDTO.getReceiveCode()))
            .collect(Collectors.toList());
        Map<ReceiveChanceStatusEnum, List<UserRightsListRes>> countResultMap = userRightsListResList.parallelStream()
            .collect(Collectors.groupingBy(UserRightsListRes::getStatus));

        List<UserRightsCountByPackSkuIdRes> receiveChanceCountList = Lists.newArrayList();
        countResultMap.forEach((receiveChanceStatus, list) -> {
            UserRightsCountByPackSkuIdRes recordItem = new UserRightsCountByPackSkuIdRes();
            if (CollectionUtils.isNotEmpty(list)) {
                String receiveCodes = list.stream().map(UserRightsListRes::getReceiveCode).distinct().collect(Collectors.joining(","));
                recordItem.setPackSkuIds(getPackSkuIdsByRuleCodes(receiveCodes, packSkuId2PromotionCodeListMap));
                recordItem.setStatus(receiveChanceStatus);
                recordItem.setCount(list.size());
            }
            receiveChanceCountList.add(recordItem);
        });

        return receiveChanceCountList;
    }

    /**
     * 根据ruleCode获取打包购skuId
     */
    private String getPackSkuIdsByRuleCodes(String userReceiveRuleCodes, Map<String, List<String>> packSkuId2RuleCodeListMap) {
        if (StringUtils.isBlank(userReceiveRuleCodes)) {
            return "";
        }
        List<String> packSkuIdsList = Lists.newArrayList();
        List<String> userReceiveRuleCodeList = Splitter.on(",").splitToList(userReceiveRuleCodes);
        for (Map.Entry<String, List<String>> map : packSkuId2RuleCodeListMap.entrySet()) {
            for (String userReceiveRuleCode : userReceiveRuleCodeList) {
                if (map.getValue().contains(userReceiveRuleCode)) {
                    packSkuIdsList.add(map.getKey());
                }
            }
        }
        return Joiner.on(",").skipNulls().join(packSkuIdsList);
    }

    private List<UserRightsListRes> filterRMSSkuId(List<UserRightsListRes> resultList) {
        List<String> rmsSkuIdList = rmsService.queryAssetRMSSkuIdList();
        if (CollectionUtils.isEmpty(rmsSkuIdList)) {
            return Collections.EMPTY_LIST;
        }
        //获取skuId对应的promotionCode
        Map<String, SkuPromotionRes> skuId2PromotionMap = skuService.skuId2PromotionMapFromCache(Joiner.on(",").join(rmsSkuIdList));
        if (MapUtils.isEmpty(skuId2PromotionMap)) {
            log.error("[skuPromotionResDTOList null][rmsSkuIdList:{}]", rmsSkuIdList);
            return Collections.EMPTY_LIST;
        }

        //取rms里配置的商品资格
        List<UserRightsListRes> showUserReceiveList = Lists.newArrayList();
        for (UserRightsListRes userReceiveChance : resultList) {
            fillUserReceiveChanceResDTOSkuId(skuId2PromotionMap, userReceiveChance);
            if (rmsSkuIdList.contains(userReceiveChance.getSkuId())) {
                showUserReceiveList.add(userReceiveChance);
            }
        }
        return showUserReceiveList;
    }

    private void fillUserReceiveChanceResDTOSkuId(Map<String, SkuPromotionRes> skuId2SkuPromotionMap, UserRightsListRes userReceiveChance) {
        if (StringUtils.isNotBlank(userReceiveChance.getSkuId())) {
            return;
        }
        for (Map.Entry<String, SkuPromotionRes> entry : skuId2SkuPromotionMap.entrySet()) {
            SkuPromotionRes skuPromotionResDTO = entry.getValue();
            if (skuPromotionResDTO.getPromotionCode().equals(userReceiveChance.getReceiveCode())) {
                userReceiveChance.setSkuId(skuPromotionResDTO.getSkuId());
            }
        }
    }

    /**
     * 查询用户记录，按照presentNo、filterStatus筛选，按照orderType排序输出
     */
    private List<UserRightsListRes> queryUserRightsListResByCon(List<ReceiveRightRecord> receiveRightRecordList, String presentNo, Set<ReceiveChanceStatusEnum> filterStatus, ReceiveChanceOrderEnum orderType) {
        Date now = new Date();
        Map<String, AccountTypeEnum> promotionAccountTypeMap = Maps.newConcurrentMap();

        // 筛选+排序，按照购买时间倒序
        Stream<ReceiveRightRecord> stream = receiveRightRecordList.parallelStream()
            .filter(item -> StringUtils.isNotBlank(presentNo) ? StringUtils.equals(item.getPresentNo(), presentNo) : true);

        if (orderType == null) {
            orderType = ReceiveChanceOrderEnum.ORDER_TIME_ASC;
        }
        stream = stream.sorted(Comparator.comparing(ReceiveRightRecord::getReceiveStatus));
        switch (orderType) {
            case RECEIVE_TIME_ASC:
                stream = stream.sorted(Comparator
                    .comparing(ReceiveRightRecord::getReceiveTime, Comparator.nullsLast(Date::compareTo)));
                break;
            case RECEIVE_TIME_DESC:
                stream = stream.sorted(Comparator
                    .comparing(ReceiveRightRecord::getReceiveTime, Comparator.nullsLast(Date::compareTo)).reversed());
                break;
            case ORDER_TIME_ASC:
                stream = stream.sorted(Comparator
                    .comparing(ReceiveRightRecord::getOrderTime, Comparator.nullsLast(Date::compareTo)));
                break;
            case ORDER_TIME_DESC:
                stream = stream.sorted(Comparator
                    .comparing(ReceiveRightRecord::getOrderTime, Comparator.nullsLast(Date::compareTo)).reversed());
                break;
            case INVALID_TIME_ASC:
                stream = stream.sorted(Comparator
                    .comparing(ReceiveRightRecord::getReceiveDeadlineTime, Comparator.nullsLast(Date::compareTo)));
                break;
            case INVALID_TIME_DESC:
                stream = stream.sorted(Comparator
                    .comparing(ReceiveRightRecord::getReceiveDeadlineTime, Comparator.nullsLast(Date::compareTo)).reversed());
                break;
            default:
                stream = stream.sorted(Comparator
                    .comparing(ReceiveRightRecord::getOrderTime));
        }
        stream = stream.filter(item -> StringUtils.isNotBlank(item.getPromotionCode()));
        List<UserRightsListRes> resultList = stream.map(item -> {
            UserRightsListRes recordItem = new UserRightsListRes();
            recordItem.setReceiveCode(item.getPromotionCode());
            recordItem.setOrderCode(item.getOrderCode());
            if (item.getOrderTime() != null) {
                recordItem.setCreateTime(item.getOrderTime().getTime());
            }

            Pair<ReceiveChanceStatusEnum, ReceiveChanceInvalidTypeEnum> receiveChanceStatus = parseReceiveChanceStatus(item, now);
            recordItem.setStatus(receiveChanceStatus.getKey());
            recordItem.setInvalidType(receiveChanceStatus.getValue());
            recordItem.setRefundStatus(item.getRefundStatus());
            if (item.getRefundTime() != null) {
                recordItem.setRefundTime(item.getRefundTime().getTime());
            }
            recordItem.setReceiveStatus(item.getReceiveStatus());
            if (item.getReceiveDeadlineTime() != null) {
                recordItem.setReceiveDeadline(item.getReceiveDeadlineTime().getTime());
            }
            // 2021-11-24 VIPDEV-11949 已买联名展示优化，领取账号
            String promotionCode = item.getPromotionCode();

            // 京东领取账号特殊处理
            String receiveAccount = item.getEncryptAccount();
            if (StringUtils.isNotBlank(receiveAccount)) {
                AccountTypeEnum accountType = null;
                if (promotionAccountTypeMap.containsKey(promotionCode)) {
                    accountType = promotionAccountTypeMap.get(promotionCode);
                } else {
                    Goods goods = goodsRepository.queryByPromotionCodeFromCache(promotionCode);
                    Gift gift = null;
                    if ((goods != null) && StringUtils.isNotBlank(goods.getGiftCode())) {
                        gift = giftRepository.queryByCodeFromCache(goods.getGiftCode());
                    }
                    if (gift == null) {
                        log.warn("Gift Not Found, PromotionCode: {}", promotionCode);
                    } else {
                        UrlConfig notifyUrlConfig = urlConfigRepository.queryByIdFromCache(gift.getSendNotifyUrlId());
                        if (notifyUrlConfig != null) {
                            accountType = AccountTypeEnum.of(notifyUrlConfig.getAccountType());
                        }
                    }
                    if (accountType == null) {
                        accountType = AccountTypeEnum.MOBILE;
                    }
                    promotionAccountTypeMap.put(promotionCode, accountType);
                }
                switch (accountType) {
                    case MOBILE:
                        receiveAccount = org.apache.commons.lang.StringUtils.left(receiveAccount, 3) + "****"
                            + org.apache.commons.lang.StringUtils.right(receiveAccount, 4);
                        break;
                    case INPUT_USER_ACCOUNT:
                        receiveAccount = org.apache.commons.lang.StringUtils.left(receiveAccount, 2) + "****"
                            + org.apache.commons.lang.StringUtils.right(receiveAccount, 2);
                        break;
                    case UID:
                    case THIRD_AUTH_TOKEN:
                        break;
                }
            }
            recordItem.setReceiveAccount(receiveAccount);
            if (item.getReceiveTime() != null) {
                recordItem.setReceiveTime(item.getReceiveTime().getTime());
            }
            recordItem.setPresentNo(item.getPresentNo());
            recordItem.setSkuId(item.getSkuId());
            return recordItem;
        }).filter(item -> CollectionUtils.isEmpty(filterStatus) || (CollectionUtils.isNotEmpty(filterStatus)
            && filterStatus.contains(item.getStatus())))
            .collect(Collectors.toList());
        return resultList;
    }

    private Set<ReceiveChanceStatusEnum> convert2ReceiveChanceStatusEnumSet(String statusListQryStr) {
        final Set<ReceiveChanceStatusEnum> filterStatus = Sets.newHashSet();
        if (StringUtils.isBlank(statusListQryStr)) {
            return filterStatus;
        }
        String[] arrStatus = StringUtils.split(statusListQryStr, ",");
        for (String strStatus : arrStatus) {
            strStatus = StringUtils.trimToEmpty(strStatus);
            if (!StringUtils.isNumeric(strStatus)) {
                log.error("[error statusQry:{}]", strStatus);
                throw new BizRuntimeException(CodeEnum.ERROR_PARAM);
            }

            int intReceiveStatus = NumberUtils.toInt(strStatus, -1);
            ReceiveChanceStatusEnum receiveChanceStatus = ReceiveChanceStatusEnum.of(intReceiveStatus);
            if (receiveChanceStatus == null) {
                log.error("[error statusQry:{}]", strStatus);
                throw new BizRuntimeException(CodeEnum.ERROR_PARAM);
            }
            filterStatus.add(receiveChanceStatus);
        }
        return filterStatus;
    }

    /*
     * 转换领取资格状态
     */
    private Pair<ReceiveChanceStatusEnum, ReceiveChanceInvalidTypeEnum> parseReceiveChanceStatus(ReceiveRightRecord receiveRightRecord, Date time) {
        if (receiveRightRecord.getReceiveStatus() != null) {
            ReceiveStatusEnum receiveStatus = ReceiveStatusEnum.of(receiveRightRecord.getReceiveStatus());

            // 已领取（receiveStatus = 1）
            if (receiveStatus == ReceiveStatusEnum.RECEIVED) {
                ReceiveRightRecordRefundStatus refundStatus = ReceiveRightRecordRefundStatus.of(receiveRightRecord.getRefundStatus());
                if (refundStatus == null) {
                    refundStatus = ReceiveRightRecordRefundStatus.UN_REFUND;
                }

                // 已失效-已退单（receiveStatus = 0 且 refund_status非已退单和退单失败）
                if ((refundStatus == ReceiveRightRecordRefundStatus.REFUND_SUC)
                    || (refundStatus == ReceiveRightRecordRefundStatus.REFUND_OFFLINE_SUC)
                    || (refundStatus == ReceiveRightRecordRefundStatus.REFUND_FAIL)) {
                    return Pair.of(ReceiveChanceStatusEnum.INVALID, ReceiveChanceInvalidTypeEnum.REFUND);
                }

                return Pair.of(ReceiveChanceStatusEnum.RECEIVED, null);
            } else if (receiveStatus == ReceiveStatusEnum.PENDING_RECEIVE) {
                ReceiveRightRecordRefundStatus refundStatus = ReceiveRightRecordRefundStatus.of(receiveRightRecord.getRefundStatus());
                if (refundStatus == null) {
                    refundStatus = ReceiveRightRecordRefundStatus.UN_REFUND;
                }

                // 已失效-已退单（receiveStatus = 0 且 refund_status非已退单和退单失败）
                if ((refundStatus == ReceiveRightRecordRefundStatus.REFUND_SUC)
                    || (refundStatus == ReceiveRightRecordRefundStatus.REFUND_OFFLINE_SUC)
                    || (refundStatus == ReceiveRightRecordRefundStatus.REFUND_FAIL)) {
                    return Pair.of(ReceiveChanceStatusEnum.INVALID, ReceiveChanceInvalidTypeEnum.REFUND);
                }

                // 已失效-资格过期（receiveStatus = 0 且 receive_deadline小于当前时间）
                else if ((receiveRightRecord.getReceiveDeadlineTime() != null) && (receiveRightRecord.getReceiveDeadlineTime().compareTo(time) < 0)) {
                    return Pair.of(ReceiveChanceStatusEnum.INVALID, ReceiveChanceInvalidTypeEnum.EXPIRED);
                }

                // 未领取（receiveStatus = 0 且 未过期、未退单）
                else {
                    return Pair.of(ReceiveChanceStatusEnum.PENDING_RECEIVE, null);
                }
            }
        }

        return Pair.of(ReceiveChanceStatusEnum.UN_KNOWN, null);
    }

    /**
     * case1:用户买了同一个skuId的多笔订单，一次性领取多笔订单;
     * <p></p>
     * case2:用户买了打包购skuId，领取时一次性多个子skuId的订单
     *
     * @return 只返回最终领取结果，若有失败的，返回领取失败的结果
     */
    private BaseResponse<PartnerRightsResponse> receiveRights(ReceiveRightsReq receiveRightsReq) {
        List<OpenOrderRights> openOrderRightsList = RightsFactory
            .buildByReceiveRights(receiveRightsReq, skuRepository, receiveRecordRepository, orderService, spuFulfillmentRepository, giftService);
        AssertUtils.notEmpty(openOrderRightsList, CodeEnum.ERROR_NO_RIGHT);

        List<BaseResponse<PartnerRightsResponse>> responseList = Lists.newArrayList();
        for (OpenOrderRights openOrderRights : openOrderRightsList) {
            BaseResponse<PartnerRightsResponse> partnerRightsResponse = openRights(openOrderRights);
            responseList.add(partnerRightsResponse);
            if (!partnerRightsResponse.suc()) {
                break;
            }
        }
        for (BaseResponse<PartnerRightsResponse> response : responseList) {
            //有一个领失败的，直接提示领取失败
            if (!CodeEnum.SUCCESS.getCode().equals(response.getCode())) {
                return response;
            }
        }
        return responseList.get(0);
    }

    @Override
    public CheckOrderCanTerminateDetailRes checkCanTerminateRights(CheckOrderCanTerminateDetail req) {
        if (SpuEnum.COUPON_SPU.getSpuId().equals(req.getSku().getSpuId())) {
            return couponService.checkCanTerminateRights(req);
        }
        return new CheckOrderCanTerminateDetailRes(req.getReq().getOrderCode(), CodeEnum.SUCCESS);
    }

    @Override
    public void batchAskPartnerCanBuy(Long uid, List<SkuBeforeBuyCheckAggregate> skuBeforeBuyCheckAggregateList) {
        if (null == uid || CollectionUtils.isEmpty(skuBeforeBuyCheckAggregateList)) {
            return;
        }
        try {
            Map<Long, AskPartnerCanBuyByUrlIdDetailReq> urlId2AskPartnerReqMap = Maps.newHashMap();
            for (SkuBeforeBuyCheckAggregate skuBeforeBuyCheckAggregate : skuBeforeBuyCheckAggregateList) {
                if (null != skuBeforeBuyCheckAggregate.getGift() && null != skuBeforeBuyCheckAggregate.getGift().getBeforeCheckUrlId()) {
                    UrlConfig urlConfig = urlConfigRepository.queryByIdFromCache(skuBeforeBuyCheckAggregate.getGift().getBeforeCheckUrlId());
                    if (null == urlConfig || StringUtils.isBlank(urlConfig.getSyncUrl())) {
                        continue;
                    }
                    urlId2AskPartnerReqMap.put(skuBeforeBuyCheckAggregate.getGift()
                        .getBeforeCheckUrlId(), RightsFactory.buildAskPartnerReq(skuBeforeBuyCheckAggregate));
                }
            }
            if (MapUtils.isEmpty(urlId2AskPartnerReqMap)) {
                return;
            }
            //调用合作方验证用户能否购买
            AskPartnerCanBuyByUrlIdReq req = new AskPartnerCanBuyByUrlIdReq();
            req.setUid(uid);
            req.setDetailReqList(urlId2AskPartnerReqMap.values().stream().collect(Collectors.toList()));
            List<AskPartnerCanBuyByUrlIdDetailRes> askPartnerResultList = rightsRepository.batchAskPartnerCanBuyByUrlId(req);
            if (CollectionUtils.isEmpty(askPartnerResultList)) {
                log.error("[askPartner fail][skuBeforeBuyCheckAggregateList:{}]", skuBeforeBuyCheckAggregateList);
                return;
            }
            log.info("[end][req:{},askPartnerResultList:{}]", req, askPartnerResultList);
            Map<Long, AskPartnerCanBuyByUrlIdDetailRes> urlId2AskPartnerResult = askPartnerResultList.stream()
                .collect(Collectors.toMap(AskPartnerCanBuyByUrlIdDetailRes::getUrlId, Function.identity(), (key1, key2) -> key2));
            for (SkuBeforeBuyCheckAggregate skuBeforeBuyCheckAggregate : skuBeforeBuyCheckAggregateList) {
                Long beforeCheckUrlId = skuBeforeBuyCheckAggregate.getGift().getBeforeCheckUrlId();
                if (null == beforeCheckUrlId) {
                    continue;
                }
                AskPartnerCanBuyByUrlIdDetailRes askPartnerResult = urlId2AskPartnerResult.get(beforeCheckUrlId);
                if (null == askPartnerResult) {
                    log.info("[askPartnerResult null][beforeCheckUrlId:{}]", beforeCheckUrlId);
                    continue;
                }
                SkuBeforeBuyCheckDetailRes skuBeforeBuyCheckDetailRes
                    = new SkuBeforeBuyCheckDetailRes(skuBeforeBuyCheckAggregate.getSkuId(), skuBeforeBuyCheckAggregate
                    .getSkuAmount(), askPartnerResult);
                skuBeforeBuyCheckAggregate.setCheckResult(skuBeforeBuyCheckDetailRes);
                if (!YesOrNoEnum.YES.getValue().equals(skuBeforeBuyCheckDetailRes.getCanBuy())) {
                    skuBeforeBuyCheckAggregate.setNeedNextCheck(false);
                }
            }
        } catch (Exception e) {
            log.error("[Exception][skuBeforeBuyCheckAggregateList:{}]", skuBeforeBuyCheckAggregateList, e);
        }
    }

    @Override
    public List<CollectUserInfoRes> collectUserInfo(CollectUserInfoReq req) {
        AssertUtils.notNull(req.getUid(), CodeEnum.ERROR_PARAM);
        AssertUtils.notBlank(req.getInfoId(), CodeEnum.ERROR_PARAM);
        List<ReceiveRightRecord> userRightsList = receiveRecordRepository.queryByConFromMySql(ReceiveRightRecordQryCon.builder()
            .uid(Long.valueOf(req.getUid()))
            .build());
        AssertUtils.notEmpty(userRightsList, CodeEnum.ERROR_USER_NO_RIGHT_RECORD);
        Map<String, List<ReceiveRightRecord>> account2RightsMap = userRightsList.stream()
            .filter(receiveRightRecord -> StringUtils.isNotBlank(receiveRightRecord.getEncryptAccount()))
            .collect(Collectors.groupingBy(ReceiveRightRecord::getEncryptAccount));
        if (MapUtils.isEmpty(account2RightsMap)) {
            return Collections.EMPTY_LIST;
        }

        List<CollectUserInfoScenesRes> scenes = Lists.newArrayList();
        scenes.add(CollectUserInfoFactory.buildCollectUserInfoScenesRes(account2RightsMap));

        //组织返回结果
        List<CollectUserInfoRes> resList = Lists.newArrayList();
        List<String> reqInfoIdList = Splitter.on(",").splitToList(req.getInfoId());
        for (String infoId : reqInfoIdList) {
            resList.add(CollectUserInfoRes.builder().infoId(infoId).scenes(scenes).build());
        }
        return resList;
    }

    @Override
    public void notifyRefunded(NotifyRefundedReq req) {
        log.info("[start][req:{}]", req);
        OrderDto paidOrder = orderService.queryByOrderCode(req.getOrderCode());
        AssertUtils.notNull(paidOrder, CodeEnum.ERROR_ORDER_NON);
        ReceiveRightRecordQryCon qryCon = ReceiveRightRecordQryCon.builder()
            .orderCode(paidOrder.getOrderCode())
            .uid(paidOrder.getUserId())
            .skuId(paidOrder.getSkuId())
            .build();
        List<ReceiveRightRecord> recordList = rightsRecordRepository.queryByConFromMySql(qryCon);
        AssertUtils.notEmpty(recordList, CodeEnum.ERROR_ORDER_NON);
        ReceiveRightRecord paidRightRecord = recordList.get(0);
        if (ReceiveRightRecordRefundStatus.isRefundSuc(paidRightRecord.getRefundStatus())) {
            log.info("[already refund][orderCode:{}]", paidRightRecord.getOrderCode());
            return;
        }
        if (null != req.getRefundAmount() && null != paidRightRecord.getAmount() && req.getRefundAmount() > paidRightRecord.getAmount()) {
            throw new BizRuntimeException(CodeEnum.ERROR_REFUND_AMOUNT);
        }
        paidRightRecord.setRefundAmount(req.getRefundAmount());
        paidRightRecord.setRefundRes("refundByPartner");
        paidRightRecord.setRefundTime(new Date());
        paidRightRecord.setRefundStatus(ReceiveRightRecordRefundStatus.REFUND_SUC.getStatus());
        receiveRecordRepository.updateByOrderCodeSelective(paidRightRecord);
        log.info("[success][req:{},paidOrder:{}]", req, paidOrder);
    }

}
