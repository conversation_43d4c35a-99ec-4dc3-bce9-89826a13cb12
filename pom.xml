<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.7.8</version>
  </parent>

  <packaging>pom</packaging>
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.iqiyi.vip</groupId>
  <artifactId>vip-fulfillment-center</artifactId>
  <version>6.0.54</version>
  <name>vip-fulfillment-center</name>

    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <sentinel.version>1.8.0-iqiyi-4</sentinel.version>
        <sharding-jdbc.version>4.0.0-RC2</sharding-jdbc.version>
        <kms-mybatis-start.version>2.1.1-0.2</kms-mybatis-start.version>
        <druid.version>1.2.4</druid.version>
        <hutool-all.version>5.7.20</hutool-all.version>
        <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
        <org.projectlombok.version>1.18.20</org.projectlombok.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <v.spring-cloud.version>v-1.3.0-boot-2.7.x</v.spring-cloud.version>
        <log4j.version>2.17.2</log4j.version>
        <jackson.version>********</jackson.version>
        <dataservice-client.version>1.1.2</dataservice-client.version>
        <qiyi-job.version>2.4.9-RELEASE</qiyi-job.version>
        <qiyi-job-shade.version>0.0.10</qiyi-job-shade.version>
        <v-eagle.version>0.2.22.1-RELEASE</v-eagle.version>
        <async-task.version>1.0.15</async-task.version>
        <fastjson.version>1.2.83_noneautotype</fastjson.version>
        <jetcache-starter-redis-lettuce.version>2.7.7</jetcache-starter-redis-lettuce.version>
        <skywalking.version>6.1.0-iqiyi-7</skywalking.version>
        <config-client.version>3.15.7</config-client.version>
    </properties>

  <dependencies>
      <dependency>
          <groupId>com.iqiyi.config</groupId>
          <artifactId>config-client</artifactId>
          <version>${config-client.version}</version>
      </dependency>
      <!-- Explicit Guava dependency to resolve version conflict with config-client -->
      <dependency>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
          <version>31.1-jre</version>
      </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
      <exclusions>
        <!-- 去掉springboot默认配置 -->
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
      <dependency>
          <groupId>io.springfox</groupId>
          <artifactId>springfox-boot-starter</artifactId>
          <version>3.0.0</version>
      </dependency>
      <!-- 高性能对象映射 -->
      <dependency>
          <groupId>org.mapstruct</groupId>
          <artifactId>mapstruct</artifactId>
          <version>${org.mapstruct.version}</version>
      </dependency>
      <dependency>
          <groupId>com.alibaba</groupId>
          <artifactId>fastjson</artifactId>
          <version>${fastjson.version}</version>
      </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-aop</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

      <dependency>
          <groupId>org.projectlombok</groupId>
          <artifactId>lombok</artifactId>
          <version>${org.projectlombok.version}</version>
          <scope>provided</scope>
      </dependency>
      <dependency>
          <groupId>org.projectlombok</groupId>
          <artifactId>lombok-mapstruct-binding</artifactId>
          <version>${lombok-mapstruct-binding.version}</version>
      </dependency>

      <dependency>
          <groupId>org.mapstruct</groupId>
          <artifactId>mapstruct-processor</artifactId>
          <version>${org.mapstruct.version}</version>
          <scope>provided</scope>
      </dependency>
    <!--eagle-->
    <dependency>
        <groupId>com.iqiyi.v</groupId>
        <artifactId>v-spring-boot-starter-eagle</artifactId>
        <version>${v-eagle.version}</version>
    </dependency>

      <!--task-->
      <dependency>
          <groupId>com.iqiyi.vip</groupId>
          <artifactId>async-task-core</artifactId>
          <version>${async-task.version}</version>
          <exclusions>
              <exclusion>
                  <groupId>com.iqiyi.config</groupId>
                  <artifactId>sentinel-datasource-apollo</artifactId>
              </exclusion>
              <exclusion>
                  <groupId>com.iqiyi.vip</groupId>
                  <artifactId>sentinel-extension-iqiyi</artifactId>
              </exclusion>
          </exclusions>
      </dependency>

      <!--job-->
      <dependency>
          <groupId>com.iqiyi.vip</groupId>
          <artifactId>vip-job-core</artifactId>
          <version>0.2.3</version>
      </dependency>

      <dependency>
          <groupId>com.mysql</groupId>
          <artifactId>mysql-connector-j</artifactId>
          <scope>runtime</scope>
      </dependency>
    <!--mybatis-->
    <dependency>
      <groupId>org.mybatis.spring.boot</groupId>
      <artifactId>mybatis-spring-boot-starter</artifactId>
      <version>2.2.2</version>
    </dependency>

      <!--rocketmq-->
      <dependency>
          <groupId>org.apache.rocketmq</groupId>
          <artifactId>rocketmq-spring-boot-starter</artifactId>
          <version>2.1.0-iqiyi-2</version>
      </dependency>

    <!--sharding-->
    <dependency>
      <groupId>org.apache.shardingsphere</groupId>
      <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
      <version>4.0.0-RC2</version>
    </dependency>

    <!--vip-biz-api mail password-->

    <!--commons-->
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.12.0</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-collections4</artifactId>
      <version>4.4</version>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
      <version>1.15</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-text</artifactId>
      <version>1.9</version>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <version>4.5.12</version>
    </dependency>

    <!--sentinel-->
      <!-- 核心依赖，必须引入 -->
      <dependency>
          <groupId>com.alibaba.csp</groupId>
          <artifactId>sentinel-core</artifactId>
          <version>${sentinel.version}</version>
      </dependency>

      <!-- 簇点链路功能 -->
      <dependency>
          <groupId>com.alibaba.csp</groupId>
          <artifactId>sentinel-transport-simple-http</artifactId>
          <version>${sentinel.version}</version>
      </dependency>
      <!-- 配置中心动态规则管理 -->
      <dependency>
          <groupId>com.alibaba.csp</groupId>
          <artifactId>sentinel-datasource-apollo</artifactId>
          <version>${sentinel.version}</version>
          <exclusions>
              <exclusion>
                  <artifactId>config-client</artifactId>
                  <groupId>com.iqiyi.config</groupId>
              </exclusion>
          </exclusions>
      </dependency>
      <!-- 对接全链路平台Prometheus指标监控 -->
      <dependency>
          <groupId>com.alibaba.csp</groupId>
          <artifactId>sentinel-metric-prometheus</artifactId>
          <version>${sentinel.version}</version>
      </dependency>
      <!-- sentinel 热点参数限流必须引入 -->
      <dependency>
          <groupId>com.alibaba.csp</groupId>
          <artifactId>sentinel-parameter-flow-control</artifactId>
          <version>${sentinel.version}</version>
      </dependency>
      <!-- sentinel切面，配合@SentinelResource注解使用 -->
      <dependency>
          <groupId>com.alibaba.csp</groupId>
          <artifactId>sentinel-annotation-aspectj</artifactId>
          <version>${sentinel.version}</version>
      </dependency>

      <dependency>
          <groupId>com.alibaba.csp</groupId>
          <artifactId>sentinel-apache-httpclient-adapter</artifactId>
          <version>${sentinel.version}</version>
      </dependency>
      <!-- Spring WebMvc jar引入 -->
      <dependency>
          <groupId>com.alibaba.csp</groupId>
          <artifactId>sentinel-spring-webmvc-adapter</artifactId>
          <version>${sentinel.version}</version>
      </dependency>

    <!--cache-->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-cache</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>
    <dependency>
      <groupId>redis.clients</groupId>
      <artifactId>jedis</artifactId>
      <version>3.3.0</version>
    </dependency>
    <dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson-spring-boot-starter</artifactId>
      <version>3.15.5</version>
    </dependency>
    <dependency>
      <groupId>com.github.ben-manes.caffeine</groupId>
      <artifactId>caffeine</artifactId>
    </dependency>

      <dependency>
          <groupId>com.iqiyi.vip.kms</groupId>
          <artifactId>vip-kms-mybatis-spring-boot-starter</artifactId>
          <version>${kms-mybatis-start.version}</version>
      </dependency>

      <dependency>
          <groupId>cn.hutool</groupId>
          <artifactId>hutool-all</artifactId>
          <version>${hutool-all.version}</version>
      </dependency>

      <dependency>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-api</artifactId>
          <version>${log4j.version}</version>
      </dependency>
      <dependency>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-core</artifactId>
          <version>${log4j.version}</version>
      </dependency>

      <dependency>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-databind</artifactId>
          <version>${jackson.version}</version>
      </dependency>

      <!--会员交易client-->
      <dependency>
          <groupId>com.qiyi.vip.trade</groupId>
          <artifactId>dataservice-client</artifactId>
          <version>${dataservice-client.version}</version>
      </dependency>

      <dependency>
          <groupId>com.alicp.jetcache</groupId>
          <artifactId>jetcache-starter-redis-lettuce</artifactId>
          <version>${jetcache-starter-redis-lettuce.version}</version>
      </dependency>

      <dependency>
          <groupId>org.apache.skywalking</groupId>
          <artifactId>apm-toolkit-logback-1.x</artifactId>
          <version>${skywalking.version}</version>
      </dependency>
  </dependencies>

  <repositories>
    <repository>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>libs-release</id>
      <name>libs-release</name>
      <url>http://jfrog.cloud.qiyi.domain/libs-release</url>
    </repository>
    <repository>
      <snapshots/>
      <id>libs-snapshot</id>
      <name>libs-snapshot</name>
      <url>http://jfrog.cloud.qiyi.domain/libs-snapshot</url>
    </repository>
    <!-- cloudservice -->
    <repository>
      <snapshots/>
      <id>iqiyi-maven-middleware</id>
      <name>iqiyi-maven-middleware</name>
      <url>http://jfrog.cloud.qiyi.domain/iqiyi-maven-cloudservice</url>
    </repository>
  </repositories>

<modules>
    <module>vip-fulfillment-center-api</module>
    <module>vip-fulfillment-center-admin</module>
    <module>vip-fulfillment-center-task</module>
    <module>vip-fulfillment-center-worker</module>
    <module>vip-fulfillment-center-job</module>
    <module>vip-fulfillment-center-app</module>
    <module>vip-fulfillment-center-domain</module>
    <module>vip-fulfillment-center-infrastructure</module>
    <module>vip-fulfillment-center-common</module>
    <module>vip-fulfillment-center-vip-worker</module>
</modules>
</project>
