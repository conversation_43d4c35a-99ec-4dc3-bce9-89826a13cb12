logging:
    config: classpath:logback-spring.xml

spring:
  profiles:
    active: test
  jackson:
    serialization:
      write-dates-as-timestamps: true
  cache:
    type: caffeine
  main:
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
    view:
      prefix: /pages/
      suffix: .html
  servlet:
    multipart:
      max-file-size: 100MB

mybatis:
  mapper-locations: classpath:mapper/*.xml,classpath:mapper/extend/*.xml,classpath:mapper/tidb/*.xml

rocketmq:
  producer:
    enabled: false

management:
  server:
    port: 8099
  endpoints:
    web:
      exposure:
        include: health,prometheus
log:
#  home: ~/data/logs
  home: /qke/log

vip:
  kms:
    cache: hutool-fifo
    cache-capacity: 200
    cache-timeout: 60000
    close-parameter-tampering: true
    refresh-time: 1800
    table-for-encrypt:
      rights_record:
        close-def-encrypt-decrypt: false
        table-column: encrypt_mobile,encrypt_account,coupon_code
    valid-period: 300

async:
  task:
    execute: false
    execute.cluster: false
    table:
      name: fulfillment_center_async_task

# jetcache缓存配置
jetcache:
  penetrationProtect: true
  areaInCacheName: false
  statIntervalMinutes: 3
  hidePackages: com.alibaba
  local:
    default:
      type: caffeine
      keyConvertor: fastjson2
      limit: 10000
      expireAfterWriteInMillis: 1800000